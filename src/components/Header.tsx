import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { UtensilsC<PERSON><PERSON>, Bar<PERSON>hart3, <PERSON><PERSON><PERSON><PERSON>ist, <PERSON>u, X } from "lucide-react";
import { Button } from "@/components/ui/button";

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  const isActive = (path: string) => location.pathname === path;

  return (
    <header className="bg-gradient-primary shadow-elegant sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            <div className="bg-white/20 p-2 rounded-lg">
              <UtensilsCrossed className="h-6 w-6 text-white" />
            </div>
            <h1 className="text-white text-xl font-bold hidden sm:block">
              Đặt Cơm Công Ty
            </h1>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-2">
            <Button
              variant={isActive('/orders') ? 'secondary' : 'ghost'}
              asChild
              className={isActive('/orders')
                ? 'bg-white/20 text-white hover:bg-white/30'
                : 'text-white/80 hover:bg-white/10 hover:text-white'
              }
            >
              <Link to="/orders">
                <UtensilsCrossed className="h-4 w-4 mr-2" />
                Đặt Cơm
              </Link>
            </Button>

            <Button
              variant={isActive('/order-manage') ? 'secondary' : 'ghost'}
              asChild
              className={isActive('/order-manage')
                ? 'bg-white/20 text-white hover:bg-white/30'
                : 'text-white/80 hover:bg-white/10 hover:text-white'
              }
            >
              <Link to="/order-manage">
                <ClipboardList className="h-4 w-4 mr-2" />
                Quản lý đơn đặt
              </Link>
            </Button>

            <Button
              variant={isActive('/statistics') ? 'secondary' : 'ghost'}
              asChild
              className={isActive('/statistics')
                ? 'bg-white/20 text-white hover:bg-white/30'
                : 'text-white/80 hover:bg-white/10 hover:text-white'
              }
            >
              <Link to="/statistics">
                <BarChart3 className="h-4 w-4 mr-2" />
                Thống Kê
              </Link>
            </Button>
          </nav>

          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleMenu}
            className="md:hidden text-white hover:bg-white/10"
          >
            {isMenuOpen ? (
              <X className="h-5 w-5" />
            ) : (
              <Menu className="h-5 w-5" />
            )}
          </Button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-white/20 py-4 animate-fade-in">
            <nav className="flex flex-col space-y-2">
              <Button
                variant={isActive('/orders') ? 'secondary' : 'ghost'}
                asChild
                className={`justify-start ${isActive('/orders')
                  ? 'bg-white/20 text-white hover:bg-white/30'
                  : 'text-white/80 hover:bg-white/10 hover:text-white'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                <Link to="/orders">
                  <UtensilsCrossed className="h-4 w-4 mr-2" />
                  Đặt Cơm
                </Link>
              </Button>
              <Button
                variant={isActive('/statistics') ? 'secondary' : 'ghost'}
                asChild
                className={`justify-start ${isActive('/statistics')
                  ? 'bg-white/20 text-white hover:bg-white/30'
                  : 'text-white/80 hover:bg-white/10 hover:text-white'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                <Link to="/statistics">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Thống Kê
                </Link>
              </Button>
              <Button
                variant={isActive('/order-manage') ? 'secondary' : 'ghost'}
                asChild
                className={`justify-start ${isActive('/order-manage')
                  ? 'bg-white/20 text-white hover:bg-white/30'
                  : 'text-white/80 hover:bg-white/10 hover:text-white'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                <Link to="/order-manage">
                  <ClipboardList className="h-4 w-4 mr-2" />
                  Quản lý đơn đặt
                </Link>
              </Button>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}