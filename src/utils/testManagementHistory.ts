import { orderHistoryService } from "@/services/orderHistoryService";

/**
 * Test utility for OrderManagementPage history functionality
 */
export const testManagementHistory = {
  /**
   * Add sample history for multiple users to test the management page
   */
  async addSampleHistoryForMultipleUsers() {
    console.log("🧪 Adding sample history for multiple users...");

    const users = ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Tuấn"];
    const dishes = ["唐揚げ", "ハンバーグ", "焼き肉", "とんかつ", "みそ汁", "ごはん（小）"];

    try {
      for (const userName of users) {
        // Add order placed event
        await orderHistoryService.addHistoryEvent({
          userName,
          actionType: "order_placed",
          description: orderHistoryService.generateDescription("order_placed", userName),
          totalAmount: Math.floor(Math.random() * 500) + 300, // Random amount between 300-800
        });

        // Add some item removal events
        const randomDish = dishes[Math.floor(Math.random() * dishes.length)];
        await orderHistoryService.addHistoryEvent({
          userName,
          actionType: "item_removed",
          description: orderHistoryService.generateDescription("item_removed", userName, randomDish),
          itemName: randomDish,
          itemQuantity: 1,
        });

        // Randomly add order cancellation for some users
        if (Math.random() > 0.7) {
          await orderHistoryService.addHistoryEvent({
            userName,
            actionType: "order_cancelled",
            description: orderHistoryService.generateDescription("order_cancelled", userName),
          });
        }

        // Add another order for variety
        await orderHistoryService.addHistoryEvent({
          userName,
          actionType: "order_placed",
          description: orderHistoryService.generateDescription("order_placed", userName),
          totalAmount: Math.floor(Math.random() * 400) + 400, // Random amount between 400-800
        });

        console.log(`✅ Added history for ${userName}`);
        
        // Small delay between users
        await new Promise(resolve => setTimeout(resolve, 50));
      }

      console.log("🎉 Sample history for multiple users added successfully!");
      return { success: true, usersCount: users.length };
    } catch (error) {
      console.error("❌ Error adding sample history:", error);
      return { success: false, error: String(error) };
    }
  },

  /**
   * Test viewing history for a specific user
   */
  async testViewUserHistory(userName: string) {
    console.log(`🔍 Testing history view for ${userName}...`);

    try {
      const result = await orderHistoryService.getHistoryForUser(userName);
      
      if (result.success) {
        console.log(`✅ Found ${result.events?.length || 0} events for ${userName}:`, result.events);
        return { success: true, events: result.events };
      } else {
        console.error(`❌ Failed to fetch history for ${userName}:`, result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error(`❌ Error fetching history for ${userName}:`, error);
      return { success: false, error: String(error) };
    }
  },

  /**
   * Run full test for OrderManagementPage
   */
  async runManagementPageTest() {
    console.log("🧪 Running OrderManagementPage history test...");

    // Clear existing history
    orderHistoryService.clearAllHistory();
    console.log("🧹 Cleared existing history");

    // Add sample data for multiple users
    const addResult = await this.addSampleHistoryForMultipleUsers();
    if (!addResult.success) {
      return addResult;
    }

    // Test viewing history for each user
    const users = ["Hưng", "Ngọc", "Hoàng"];
    for (const userName of users) {
      const viewResult = await this.testViewUserHistory(userName);
      if (!viewResult.success) {
        console.warn(`⚠️ Failed to view history for ${userName}:`, viewResult.error);
      }
    }

    console.log("✅ OrderManagementPage history test completed successfully!");
    return { success: true, message: "Test completed. Navigate to OrderManagementPage and click on user names or history buttons to view history." };
  },

  /**
   * Clear all history data
   */
  clearHistory() {
    orderHistoryService.clearAllHistory();
    console.log("🧹 All history data cleared");
  },
};

// Make it available globally for testing
declare global {
  interface Window {
    testManagementHistory: typeof testManagementHistory;
  }
}

window.testManagementHistory = testManagementHistory;

// Auto-run test after a delay
setTimeout(() => {
  console.log("🚀 OrderManagementPage history test utility loaded.");
  console.log("📋 Use testManagementHistory.runManagementPageTest() to add sample data and test.");
  console.log("👥 Then navigate to OrderManagementPage and click on user names to view their history.");
}, 1000);
