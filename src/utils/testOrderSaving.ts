import { orderService } from "@/services/orderService";
import { supabaseOrderService } from "@/services/supabaseOrderService";
import { productSyncService } from "@/services/productSyncService";
import { allFoodItems } from "@/data/foodItems";
import { OrderItem } from "@/types/food";
import { debugSupabase } from "./debugSupabase";
import { testCustomerName } from "./testCustomerName";

// Interface for test results
interface TestResults {
  [key: string]: unknown;
}

/**
 * Test utility functions for verifying order saving functionality
 * These functions can be called from the browser console for testing
 */

export const testOrderSaving = {
  /**
   * Test basic order saving functionality
   */
  async testBasicOrderSave() {
    console.log("🧪 Testing basic order save...");
    
    try {
      // Create a test order with a few items
      const testItems: OrderItem[] = [
        { foodItem: allFoodItems[0], quantity: 1 },
        { foodItem: allFoodItems[5], quantity: 2 },
      ];
      
      const totalAmount = testItems.reduce((sum, item) => sum + (item.foodItem.price * item.quantity), 0);
      const customerName = `Test User ${Date.now()}`;
      
      console.log(`Creating order for ${customerName} with ${testItems.length} items, total: ¥${totalAmount}`);
      
      const result = await orderService.saveOrder(customerName, testItems, totalAmount);
      
      if (result.success && result.order) {
        console.log("✅ Order saved successfully:", result.order);
        return { success: true, orderId: result.order.id };
      } else {
        console.error("❌ Order save failed:", result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error("❌ Test failed with error:", error);
      return { success: false, error: String(error) };
    }
  },

  /**
   * Test product synchronization
   */
  async testProductSync() {
    console.log("🧪 Testing product synchronization...");

    try {
      const result = await productSyncService.syncAllProducts();

      if (result.success) {
        console.log(`✅ Product sync successful. ${result.syncedCount} products synced.`);
        return { success: true, syncedCount: result.syncedCount };
      } else {
        console.error("❌ Product sync failed:", result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error("❌ Product sync test failed:", error);
      return { success: false, error: String(error) };
    }
  },

  /**
   * Test database connection and basic query
   */
  async testDatabaseConnection() {
    console.log("🧪 Testing database connection...");

    try {
      const { supabase } = await import("@/integrations/supabase/client");

      // Test a simple query to verify connection and NULL handling
      const { data, error } = await supabase
        .from("products")
        .select("id, name")
        .is("deleted_at", null)
        .limit(1);

      if (error) {
        console.error("❌ Database connection test failed:", error);
        return { success: false, error: error.message };
      }

      console.log("✅ Database connection successful. Sample data:", data);
      return { success: true, data };
    } catch (error) {
      console.error("❌ Database connection test failed:", error);
      return { success: false, error: String(error) };
    }
  },

  /**
   * Test user creation and retrieval
   */
  async testUserManagement() {
    console.log("🧪 Testing user management...");
    
    try {
      const testUserName = `Test User ${Date.now()}`;
      
      // Test user creation
      const result = await supabaseOrderService.ensureUserExists(testUserName);
      
      if (result.success && result.userId) {
        console.log(`✅ User management test successful. User ID: ${result.userId}`);
        
        // Test finding existing user
        const result2 = await supabaseOrderService.ensureUserExists(testUserName);
        
        if (result2.success && result2.userId === result.userId) {
          console.log("✅ User retrieval test successful. Same user ID returned.");
          return { success: true, userId: result.userId };
        } else {
          console.error("❌ User retrieval test failed:", result2.error);
          return { success: false, error: result2.error };
        }
      } else {
        console.error("❌ User creation test failed:", result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error("❌ User management test failed:", error);
      return { success: false, error: String(error) };
    }
  },

  /**
   * Test order retrieval
   */
  async testOrderRetrieval(orderId: string) {
    console.log(`🧪 Testing order retrieval for ID: ${orderId}...`);
    
    try {
      const result = await supabaseOrderService.getOrderById(orderId);
      
      if (result.success && result.order) {
        console.log("✅ Order retrieval successful:", result.order);
        return { success: true, order: result.order };
      } else {
        console.error("❌ Order retrieval failed:", result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error("❌ Order retrieval test failed:", error);
      return { success: false, error: String(error) };
    }
  },

  /**
   * Run all tests in sequence
   */
  async runAllTests() {
    console.log("🚀 Running all order saving tests...");

    // First run debug tests to ensure basic functionality
    console.log("🔍 Running debug tests first...");
    const debugResults = await debugSupabase.runAllDebugTests();

    const results: AllTestResults = {
      debug: debugResults,
      customerNamePersistence: testCustomerName.runAllTests(),
      databaseConnection: await this.testDatabaseConnection(),
      productSync: await this.testProductSync(),
      userManagement: await this.testUserManagement(),
      basicOrderSave: await this.testBasicOrderSave(),
    };

    // Test order retrieval if order was created successfully
    if (results.basicOrderSave.success && results.basicOrderSave.orderId) {
      results.orderRetrieval = await this.testOrderRetrieval(results.basicOrderSave.orderId);
    }

    console.log("📊 Test Results Summary:", results);

    const allPassed = Object.values(results).every((result: any) => result.success);

    if (allPassed) {
      console.log("🎉 All tests passed!");
    } else {
      console.log("⚠️ Some tests failed. Check the results above.");
    }

    return results;
  },
};

// Make it available globally for console testing
if (typeof window !== 'undefined') {
  (window as any).testOrderSaving = testOrderSaving;
}
