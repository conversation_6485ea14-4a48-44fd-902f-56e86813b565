In the order summary card on the OrderPage component, implement the following features:

**1. Item Management Functions:**
- Add ability for users to delete individual selected items from their current order
- Add ability to delete an entire order row (cancel the whole order)
- Distinguish between "editing an order" (removing individual items) vs "canceling an order" (removing the entire order)

**2. History Icon and Dialog:**
- Add a history icon on the right side of the order summary card
- When clicked, open a modal dialog that displays the order history
- The dialog should show a chronological timeline of all order activities for the current customer

**3. History Data Requirements:**
- Retrieve history data based on the customer name stored in localStorage (using the existing useCustomerName hook)
- Track and display the following events with timestamps:
  - Order placement: "nmcong1 đã đặt món lúc 12:00"
  - Item removal/editing: "nmcong1 đã xoá món [item_name] lúc 12:03"  
  - Order cancellation: "nmcong1 đã huỷ đặt món lúc 12:05"

**4. Data Storage:**
- Review current database schema to ensure it meets the requirements for history tracking
- Create new database table(if needed) to store all history events with timestamps (if needed)
- Associate history entries with the customer name for persistence across browser sessions
- Implement proper data structure to track different types of actions (place order, edit order, cancel order)

**5. UI/UX Requirements:**
- Use consistent Vietnamese language for all messages
- Display history in reverse chronological order (newest first)
- Include proper icons for different action types
- Follow the existing design patterns and styling used in the application
- Ensure the history dialog is responsive and accessible

**Technical Notes:**
- Extend the existing localStorage utilities to handle history tracking
- Use the project's camelCase naming convention
- Implement proper error handling for localStorage operations
- Consider using the existing custom hooks pattern for history management


---

Add order history functionality to the OrderManagementPage component by integrating it into the existing "📋 Đơn đặt của người dùng" (User Orders) card.

**Requirements:**
1. **Location**: Modify only the `src/pages/OrderManagementPage.tsx` file - do NOT change the OrderPage component
2. **Integration Point**: Add the history feature to the existing Card with CardTitle "📋 Đơn đặt của người dùng" 
3. **UI Enhancement**: Add a history icon/button in the card header that opens the OrderHistoryDialog
4. **Functionality**: 
   - Reuse the existing OrderHistoryDialog component
   - Allow users to view order history for any customer by clicking on their name or a dedicated history button
   - Integrate with the existing useOrderHistory hook
5. **Design Consistency**: Follow the same design patterns used in the OrderPage implementation
6. **User Experience**: Make it easy for administrators to view order history for any customer from the management interface

**Context**: The user wants to view order history from the management page (where they can see all orders) rather than just from the individual order page. This will help administrators track customer order activities across all users.